const fs = require('fs');
const path = require('path');

/**
 * Enhanced Configuration Management System
 * Provides centralized, validated, and environment-aware configuration management
 */
class ConfigurationManager {
    constructor(configPath = './config.js', environment = 'development') {
        this.configPath = configPath;
        this.environment = environment;
        this.config = {};
        this.watchers = new Map();
        this.validators = new Map();
        
        this.loadConfiguration();
        this.setupValidators();
    }

    /**
     * Load configuration from file with environment overrides
     */
    loadConfiguration() {
        try {
            // Load base configuration
            if (fs.existsSync(this.configPath)) {
                delete require.cache[require.resolve(path.resolve(this.configPath))];
                const baseConfig = require(path.resolve(this.configPath));
                this.config = { ...this.getDefaultConfig(), ...baseConfig };
            } else {
                this.config = this.getDefaultConfig();
            }

            // Apply environment-specific overrides
            const envConfigPath = this.configPath.replace('.js', `.${this.environment}.js`);
            if (fs.existsSync(envConfigPath)) {
                delete require.cache[require.resolve(path.resolve(envConfigPath))];
                const envConfig = require(path.resolve(envConfigPath));
                this.config = { ...this.config, ...envConfig };
            }

            // Apply environment variables
            this.applyEnvironmentVariables();
            
            // Validate configuration
            this.validateConfiguration();
            
            console.log(`✅ Configuration loaded for environment: ${this.environment}`);
        } catch (error) {
            console.error(`❌ Failed to load configuration: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get default configuration values
     */
    getDefaultConfig() {
        return {
            // Application settings
            app: {
                name: 'Web Scraping System',
                version: '2.0.0',
                instanceId: `instance_${process.pid}_${Date.now()}`,
                environment: this.environment
            },

            // File paths
            files: {
                tokensFile: './tokens.json',
                paramsFile: './params.json',
                progressFile: './progress.json',
                outputDir: './output',
                logsDir: './logs',
                tempDir: './temp'
            },

            // Browser settings
            browser: {
                headless: true,
                timeout: 100000,
                pageTimeout: 100000,
                maxInstances: 5,
                launchOptions: {
                    args: ['--no-sandbox', '--disable-setuid-sandbox']
                }
            },

            // Scraping settings
            scraping: {
                baseURL: 'https://zj.stzy.com/create-paper/chapter',
                apiURL: 'https://qms.stzy.com/matrix/zw-search/api/v1/homeEs/question/textbookQuery',
                minDelay: 1000,
                maxDelay: 3000,
                maxRetries: 3,
                retryDelay: 5000,
                requestTimeout: 30000,
                maxConcurrentRequests: 3
            },

            // Token management
            tokens: {
                validationInterval: 300000, // 5 minutes
                maxInvalidAttempts: 3,
                rotationInterval: 3600000, // 1 hour (usage-based rotation)
                healthCheckInterval: 60000, // 1 minute
                lockTimeout: 30000,
                // Time-based rotation settings
                timeBasedRotation: {
                    enabled: true,
                    intervalMinutes: 30, // Rotate every 30 minutes
                    strategy: 'time_based', // 'time_based', 'usage_based', or 'hybrid'
                    maxTokenUsageTime: 1800000, // 30 minutes max usage per token
                    rotationGracePeriod: 60000 // 1 minute grace period for ongoing operations
                }
            },

            // Browser cache management
            browserCache: {
                clearOnTokenRotation: true,
                clearCache: true,
                clearCookies: true,
                clearLocalStorage: true,
                clearSessionStorage: true,
                clearIndexedDB: true,
                validateTokenAfterRotation: true,
                rotationTimeout: 30000 // 30 seconds timeout for rotation process
            },

            // Parameter management
            parameters: {
                maxConcurrentInstances: 5,
                instanceTimeout: 1800000, // 30 minutes
                stateCheckInterval: 30000, // 30 seconds
                lockTimeout: 30000,
                batchSize: 10
            },

            // Enhanced progress tracking
            progressTracking: {
                enableEnhancedTracking: true,
                saveProgressInterval: 10000, // Save progress every 10 seconds
                pageProgressTracking: true, // Track progress at page level
                resumeFromLastPage: true, // Resume from last successfully processed page
                progressValidation: true, // Validate progress data on load
                backupProgressFile: true, // Create backup of progress file
                maxProgressBackups: 5 // Keep max 5 backup files
            },

            // Error handling
            errors: {
                maxRetryAttempts: 3,
                retryBackoffMultiplier: 2,
                circuitBreakerThreshold: 5,
                circuitBreakerTimeout: 300000, // 5 minutes
                errorReportingEnabled: true
            },

            // Logging
            logging: {
                level: 'INFO',
                format: 'json',
                maxFileSize: '10MB',
                maxFiles: 5,
                enableConsole: true,
                enableFile: true,
                enableStructured: true
            },

            // Monitoring
            monitoring: {
                healthCheckInterval: 60000, // 1 minute
                metricsCollectionInterval: 30000, // 30 seconds
                alertThresholds: {
                    errorRate: 0.1, // 10%
                    responseTime: 30000, // 30 seconds
                    memoryUsage: 0.8 // 80%
                }
            },

            // Proxy settings
            proxy: {
                enabled: false,
                rotationInterval: 600000, // 10 minutes
                healthCheckInterval: 120000, // 2 minutes
                maxFailures: 3
            }
        };
    }

    /**
     * Apply environment variables to configuration
     */
    applyEnvironmentVariables() {
        const envMappings = {
            'SCRAPER_HEADLESS': 'browser.headless',
            'SCRAPER_MAX_INSTANCES': 'parameters.maxConcurrentInstances',
            'SCRAPER_LOG_LEVEL': 'logging.level',
            'SCRAPER_MIN_DELAY': 'scraping.minDelay',
            'SCRAPER_MAX_DELAY': 'scraping.maxDelay',
            'SCRAPER_MAX_RETRIES': 'scraping.maxRetries',
            'TOKEN_ROTATION_INTERVAL': 'tokens.timeBasedRotation.intervalMinutes',
            'TOKEN_ROTATION_ENABLED': 'tokens.timeBasedRotation.enabled',
            'BROWSER_CLEAR_CACHE': 'browserCache.clearOnTokenRotation',
            'PROGRESS_TRACKING_ENABLED': 'progressTracking.enableEnhancedTracking'
        };

        for (const [envVar, configPath] of Object.entries(envMappings)) {
            if (process.env[envVar]) {
                this.setNestedValue(this.config, configPath, this.parseEnvValue(process.env[envVar]));
            }
        }
    }

    /**
     * Parse environment variable value to appropriate type
     */
    parseEnvValue(value) {
        if (value === 'true') return true;
        if (value === 'false') return false;
        if (/^\d+$/.test(value)) return parseInt(value, 10);
        if (/^\d+\.\d+$/.test(value)) return parseFloat(value);
        return value;
    }

    /**
     * Set nested configuration value
     */
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        let current = obj;
        for (let i = 0; i < keys.length - 1; i++) {
            if (!(keys[i] in current)) {
                current[keys[i]] = {};
            }
            current = current[keys[i]];
        }
        current[keys[keys.length - 1]] = value;
    }

    /**
     * Get nested configuration value
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    /**
     * Setup configuration validators
     */
    setupValidators() {
        this.validators.set('browser.timeout', (value) => {
            if (typeof value !== 'number' || value < 1000) {
                throw new Error('Browser timeout must be a number >= 1000');
            }
        });

        this.validators.set('parameters.maxConcurrentInstances', (value) => {
            if (typeof value !== 'number' || value < 1 || value > 20) {
                throw new Error('Max concurrent instances must be between 1 and 20');
            }
        });

        this.validators.set('logging.level', (value) => {
            const validLevels = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];
            if (!validLevels.includes(value)) {
                throw new Error(`Log level must be one of: ${validLevels.join(', ')}`);
            }
        });
    }

    /**
     * Validate entire configuration
     */
    validateConfiguration() {
        for (const [path, validator] of this.validators) {
            const value = this.getNestedValue(this.config, path);
            if (value !== undefined) {
                validator(value);
            }
        }
    }

    /**
     * Get configuration value
     */
    get(path, defaultValue = undefined) {
        const value = this.getNestedValue(this.config, path);
        return value !== undefined ? value : defaultValue;
    }

    /**
     * Set configuration value
     */
    set(path, value) {
        this.setNestedValue(this.config, path, value);
        
        // Validate if validator exists
        if (this.validators.has(path)) {
            this.validators.get(path)(value);
        }
    }

    /**
     * Get entire configuration
     */
    getAll() {
        return { ...this.config };
    }

    /**
     * Watch for configuration changes
     */
    watch(path, callback) {
        if (!this.watchers.has(path)) {
            this.watchers.set(path, new Set());
        }
        this.watchers.get(path).add(callback);
    }

    /**
     * Notify watchers of configuration changes
     */
    notifyWatchers(path, newValue, oldValue) {
        if (this.watchers.has(path)) {
            for (const callback of this.watchers.get(path)) {
                try {
                    callback(newValue, oldValue, path);
                } catch (error) {
                    console.error(`Error in configuration watcher for ${path}:`, error);
                }
            }
        }
    }

    /**
     * Reload configuration from file
     */
    reload() {
        const oldConfig = { ...this.config };
        this.loadConfiguration();
        
        // Notify watchers of changes
        this.notifyConfigChanges(oldConfig, this.config);
    }

    /**
     * Notify watchers of configuration changes
     */
    notifyConfigChanges(oldConfig, newConfig, basePath = '') {
        for (const key in newConfig) {
            const currentPath = basePath ? `${basePath}.${key}` : key;
            const oldValue = this.getNestedValue(oldConfig, currentPath);
            const newValue = this.getNestedValue(newConfig, currentPath);
            
            if (typeof newValue === 'object' && newValue !== null) {
                this.notifyConfigChanges(oldConfig, newValue, currentPath);
            } else if (oldValue !== newValue) {
                this.notifyWatchers(currentPath, newValue, oldValue);
            }
        }
    }
}

module.exports = ConfigurationManager;
